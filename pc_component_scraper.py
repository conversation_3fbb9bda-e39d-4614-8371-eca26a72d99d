#!/usr/bin/env python3
"""
PC Component Price Scraper using crawl4ai

This script extracts PC component pricing information from multiple websites
and stores the data in CSV format for analysis.

Author: AI Assistant
Date: 2024
"""

import asyncio
import csv
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse

import pandas as pd
from crawl4ai import AsyncWebCrawler
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.logging import RichHandler

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)
logger = logging.getLogger("pc_scraper")
console = Console()

@dataclass
class PCComponent:
    """Data model for PC components"""
    name: str
    price: Optional[float] = None
    currency: str = "USD"
    availability: str = "Unknown"
    specifications: Dict[str, Any] = None
    category: str = "Unknown"
    brand: str = "Unknown"
    model: str = "Unknown"
    url: str = ""
    website: str = ""
    scraped_at: str = ""

    def __post_init__(self):
        if self.specifications is None:
            self.specifications = {}
        if not self.scraped_at:
            self.scraped_at = datetime.now().isoformat()

class PCComponentExtractor:
    """Main class for extracting PC component data from websites"""

    def __init__(self, config_file: str = "scraper_config.json"):
        self.config = self._load_config(config_file)
        self.components = []

    def _load_config(self, config_file: str) -> Dict:
        """Load scraper configuration"""
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file {config_file} not found. Using default configuration.")
            return self._get_default_config()

    def _get_default_config(self) -> Dict:
        """Default configuration for common PC component websites"""
        return {
            "websites": [
                {
                    "name": "newegg",
                    "base_url": "https://www.newegg.com",
                    "search_urls": {
                        "cpu": "https://www.newegg.com/Processors-Desktops/Category/ID-343",
                        "ram": "https://www.newegg.com/Desktop-Memory/Category/ID-147",
                        "videocard": "https://www.newegg.com/Video-Cards-Video-Devices/Category/ID-38",
                        "motherboard": "https://www.newegg.com/Motherboards/Category/ID-20",
                        "powersupply": "https://www.newegg.com/Power-Supplies/Category/ID-58",
                        "pc_case": "https://www.newegg.com/Computer-Cases/Category/ID-7",
                        "cooling": "https://www.newegg.com/Fans-PC-Cooling/Category/ID-573",
                        "storage": "https://www.newegg.com/Internal-SSDs/Category/ID-636"
                    },
                    "selectors": {
                        "product_container": ".item-container",
                        "name": ".item-title",
                        "price": ".price-current",
                        "availability": ".item-promo",
                        "link": ".item-title a"
                    }
                },
                {
                    "name": "amazon",
                    "base_url": "https://www.amazon.com",
                    "search_urls": {
                        "cpu": "https://www.amazon.com/s?k=cpu+processor",
                        "ram": "https://www.amazon.com/s?k=computer+memory+ram",
                        "videocard": "https://www.amazon.com/s?k=graphics+card",
                        "motherboard": "https://www.amazon.com/s?k=motherboard",
                        "powersupply": "https://www.amazon.com/s?k=power+supply+psu",
                        "pc_case": "https://www.amazon.com/s?k=computer+case",
                        "cooling": "https://www.amazon.com/s?k=cpu+cooler+fan",
                        "storage": "https://www.amazon.com/s?k=nvme+ssd"
                    },
                    "selectors": {
                        "product_container": "[data-component-type='s-search-result']",
                        "name": "h2 a span",
                        "price": ".a-price-whole",
                        "availability": ".a-size-base-plus",
                        "link": "h2 a"
                    }
                }
            ],
            "extraction_strategy": {
                "use_llm": True,
                "schema": {
                    "type": "object",
                    "properties": {
                        "products": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {"type": "string"},
                                    "price": {"type": "number"},
                                    "currency": {"type": "string"},
                                    "availability": {"type": "string"},
                                    "brand": {"type": "string"},
                                    "model": {"type": "string"},
                                    "specifications": {"type": "object"}
                                }
                            }
                        }
                    }
                }
            }
        }

    async def scrape_website(self, website_config: Dict, category: str) -> List[PCComponent]:
        """Scrape a specific website for PC components"""
        components = []
        website_name = website_config["name"]

        if category not in website_config["search_urls"]:
            logger.warning(f"Category {category} not found for {website_name}")
            return components

        url = website_config["search_urls"][category]
        logger.info(f"Scraping {website_name} for {category}: {url}")

        try:
            async with AsyncWebCrawler(verbose=True) as crawler:
                # Configure extraction strategy
                extraction_strategy = None
                if self.config.get("extraction_strategy", {}).get("use_llm", False):
                    extraction_strategy = LLMExtractionStrategy(
                        provider="ollama/llama2",  # You can change this to your preferred LLM
                        schema=self.config["extraction_strategy"]["schema"],
                        extraction_type="schema"
                    )

                result = await crawler.arun(
                    url=url,
                    extraction_strategy=extraction_strategy,
                    bypass_cache=True,
                    js_code="""
                    // Wait for dynamic content to load
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // Scroll to load more products
                    window.scrollTo(0, document.body.scrollHeight);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    """
                )

                if result.success:
                    # Parse extracted data
                    if extraction_strategy and result.extracted_content:
                        try:
                            extracted_data = json.loads(result.extracted_content)
                            products = extracted_data.get("products", [])

                            for product in products:
                                component = PCComponent(
                                    name=product.get("name", "Unknown"),
                                    price=product.get("price"),
                                    currency=product.get("currency", "USD"),
                                    availability=product.get("availability", "Unknown"),
                                    category=category,
                                    brand=product.get("brand", "Unknown"),
                                    model=product.get("model", "Unknown"),
                                    specifications=product.get("specifications", {}),
                                    url=url,
                                    website=website_name
                                )
                                components.append(component)
                        except json.JSONDecodeError:
                            logger.error(f"Failed to parse extracted JSON from {website_name}")

                    # Fallback: Parse HTML directly using selectors
                    if not components:
                        components = await self._parse_html_fallback(
                            result.html, website_config, category, url, website_name
                        )
                else:
                    logger.error(f"Failed to scrape {url}: {result.error_message}")

        except Exception as e:
            logger.error(f"Error scraping {website_name} for {category}: {str(e)}")

        logger.info(f"Extracted {len(components)} components from {website_name} ({category})")
        return components

    async def _parse_html_fallback(self, html: str, website_config: Dict,
                                 category: str, url: str, website_name: str) -> List[PCComponent]:
        """Fallback HTML parsing using BeautifulSoup when LLM extraction fails"""
        from bs4 import BeautifulSoup

        components = []
        soup = BeautifulSoup(html, 'html.parser')
        selectors = website_config.get("selectors", {})

        # Find product containers
        product_containers = soup.select(selectors.get("product_container", ".product"))

        for container in product_containers[:20]:  # Limit to first 20 products
            try:
                # Extract product name
                name_elem = container.select_one(selectors.get("name", ".product-name"))
                name = name_elem.get_text(strip=True) if name_elem else "Unknown Product"

                # Extract price
                price_elem = container.select_one(selectors.get("price", ".price"))
                price = None
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
                    if price_match:
                        try:
                            price = float(price_match.group())
                        except ValueError:
                            pass

                # Extract availability
                availability_elem = container.select_one(selectors.get("availability", ".availability"))
                availability = availability_elem.get_text(strip=True) if availability_elem else "Unknown"

                # Extract product link
                link_elem = container.select_one(selectors.get("link", "a"))
                product_url = ""
                if link_elem and link_elem.get("href"):
                    product_url = urljoin(website_config["base_url"], link_elem["href"])

                # Create component
                component = PCComponent(
                    name=name,
                    price=price,
                    availability=availability,
                    category=category,
                    url=product_url,
                    website=website_name
                )
                components.append(component)

            except Exception as e:
                logger.debug(f"Error parsing product container: {str(e)}")
                continue

        return components

    async def scrape_all_categories(self, categories: List[str] = None) -> List[PCComponent]:
        """Scrape all specified categories from all configured websites"""
        if categories is None:
            categories = ["cpu", "ram", "videocard", "motherboard", "powersupply",
                         "pc_case", "cooling", "storage"]

        all_components = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:

            for website_config in self.config["websites"]:
                website_task = progress.add_task(
                    f"Scraping {website_config['name']}...", total=len(categories)
                )

                for category in categories:
                    progress.update(website_task, description=f"Scraping {website_config['name']} - {category}")

                    components = await self.scrape_website(website_config, category)
                    all_components.extend(components)

                    progress.advance(website_task)

                    # Add delay between requests to be respectful
                    await asyncio.sleep(2)

        self.components = all_components
        logger.info(f"Total components extracted: {len(all_components)}")
        return all_components

    def save_to_csv(self, filename: str = None) -> str:
        """Save extracted components to CSV file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pc_components_{timestamp}.csv"

        if not self.components:
            logger.warning("No components to save")
            return filename

        # Convert components to dictionaries
        data = [asdict(component) for component in self.components]

        # Create DataFrame
        df = pd.DataFrame(data)

        # Clean and format data
        df['specifications'] = df['specifications'].apply(lambda x: json.dumps(x) if x else "{}")
        df['price'] = pd.to_numeric(df['price'], errors='coerce')

        # Save to CSV
        df.to_csv(filename, index=False, encoding='utf-8')
        logger.info(f"Saved {len(self.components)} components to {filename}")

        return filename

    def get_summary_stats(self) -> Dict:
        """Get summary statistics of extracted data"""
        if not self.components:
            return {}

        df = pd.DataFrame([asdict(comp) for comp in self.components])

        stats = {
            "total_components": len(self.components),
            "components_by_category": df['category'].value_counts().to_dict(),
            "components_by_website": df['website'].value_counts().to_dict(),
            "price_stats": {
                "min_price": df['price'].min(),
                "max_price": df['price'].max(),
                "avg_price": df['price'].mean(),
                "median_price": df['price'].median()
            },
            "components_with_price": df['price'].notna().sum(),
            "components_without_price": df['price'].isna().sum()
        }

        return stats


async def main():
    """Main function to run the PC component scraper"""
    import argparse

    parser = argparse.ArgumentParser(description="PC Component Price Scraper")
    parser.add_argument(
        "--config",
        default="scraper_config.json",
        help="Configuration file path (default: scraper_config.json)"
    )
    parser.add_argument(
        "--categories",
        nargs="+",
        default=["cpu", "ram", "videocard", "motherboard", "powersupply", "pc_case", "cooling", "storage"],
        help="Categories to scrape (default: all)"
    )
    parser.add_argument(
        "--output",
        help="Output CSV filename (default: auto-generated with timestamp)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    console.print("[bold green]PC Component Price Scraper[/bold green]")
    console.print(f"Categories: {', '.join(args.categories)}")
    console.print(f"Config file: {args.config}")

    # Initialize scraper
    scraper = PCComponentExtractor(args.config)

    try:
        # Scrape components
        components = await scraper.scrape_all_categories(args.categories)

        if components:
            # Save to CSV
            output_file = scraper.save_to_csv(args.output)

            # Display summary
            stats = scraper.get_summary_stats()
            console.print("\n[bold blue]Scraping Summary:[/bold blue]")
            console.print(f"Total components: {stats['total_components']}")
            console.print(f"Components with prices: {stats['components_with_price']}")
            console.print(f"Output file: {output_file}")

            # Display category breakdown
            console.print("\n[bold blue]Components by Category:[/bold blue]")
            for category, count in stats['components_by_category'].items():
                console.print(f"  {category}: {count}")

            # Display website breakdown
            console.print("\n[bold blue]Components by Website:[/bold blue]")
            for website, count in stats['components_by_website'].items():
                console.print(f"  {website}: {count}")

            # Display price statistics
            if stats['components_with_price'] > 0:
                console.print("\n[bold blue]Price Statistics:[/bold blue]")
                price_stats = stats['price_stats']
                console.print(f"  Min price: ${price_stats['min_price']:.2f}")
                console.print(f"  Max price: ${price_stats['max_price']:.2f}")
                console.print(f"  Average price: ${price_stats['avg_price']:.2f}")
                console.print(f"  Median price: ${price_stats['median_price']:.2f}")
        else:
            console.print("[red]No components were extracted. Check your configuration and network connection.[/red]")

    except Exception as e:
        logger.error(f"Error during scraping: {str(e)}")
        console.print(f"[red]Scraping failed: {str(e)}[/red]")


if __name__ == "__main__":
    asyncio.run(main())
