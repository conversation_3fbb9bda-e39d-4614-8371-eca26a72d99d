# PC Hardware Store Scraper

A generic, async web scraper for computer hardware ecommerce stores using crawl4ai and LLM-based extraction.

## Features

- **Generic Approach**: Works across multiple ecommerce sites without site-specific code
- **Async Performance**: Full async implementation for fast, concurrent scraping
- **Smart Pagination**: Automatic pagination detection and iteration
- **Resource Caching**: Built-in caching to avoid redundant requests
- **LLM Extraction**: Uses AI to intelligently extract product information
- **Multi-site Support**: Pre-configured for popular hardware stores
- **Flexible Filtering**: Filter products by price, brand, category, discounts
- **Data Export**: Save results to JSON for further analysis

## Supported Sites

Pre-configured for:
- Centre Com (Australia)
- Newegg
- Amazon
- Best Buy
- Generic ecommerce sites

## Installation

```bash
pip install -r requirements.txt
```

## Setup

1. Set your OpenAI API key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

Or set it in your code:
```python
import os
os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
```

## Quick Start

```python
import asyncio
from pc_scan import scan_hardware_store

async def main():
    # Scrape a hardware store
    results = await scan_hardware_store(
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        max_pages=5
    )
    
    print(f"Found {sum(len(r.products) for r in results)} products")

asyncio.run(main())
```

## Advanced Usage

### Custom Scraping

```python
from pc_scan import EcommerceScraper

async def custom_scrape():
    scraper = EcommerceScraper(cache_dir="./my_cache")
    
    # Scrape single page
    result = await scraper.scrape_page("https://example.com/products")
    
    # Scrape all pages
    all_results = await scraper.scrape_all_pages(
        "https://example.com/products", 
        max_pages=10
    )
    
    # Save results
    scraper.save_results(all_results, "my_results.json")
    
    return all_results
```

### Filtering Products

```python
# Filter by price range
affordable = scraper.filter_products(results, max_price=500.0)

# Filter by brand
nvidia_products = scraper.filter_products(results, brand_filter="nvidia")

# Filter discounted products only
on_sale = scraper.filter_products(results, has_discount=True)

# Combine filters
budget_nvidia = scraper.filter_products(
    results, 
    brand_filter="nvidia", 
    max_price=300.0,
    has_discount=True
)
```

## Data Structure

### ProductInfo
```python
{
    "name": "NVIDIA GeForce RTX 4080",
    "description": "High-performance graphics card...",
    "price": "$1,199.00",
    "original_price": "$1,299.00",
    "discount": "7% off",
    "product_url": "https://example.com/product/123",
    "image_url": "https://example.com/image.jpg",
    "availability": "In Stock",
    "brand": "NVIDIA",
    "category": "Graphics Cards"
}
```

### Output JSON Structure
```python
{
    "total_products": 150,
    "total_pages_scraped": 5,
    "products": [...],
    "scraping_metadata": {
        "pages_scraped": ["url1", "url2", ...],
        "timestamp": 1234567890
    }
}
```

## Configuration

### Adding New Sites

```python
from pc_scan import SiteConfig

custom_config = SiteConfig(
    name="My Store",
    base_url="https://mystore.com",
    product_list_selectors=[".product-item", ".item"],
    pagination_selectors=[".pagination"],
    custom_instructions="Focus on specific product types..."
)

# Add to scraper
scraper.site_configs["mystore"] = custom_config
```

### Cache Management

```python
# Disable caching
result = await scraper.scrape_page(url, use_cache=False)

# Custom cache directory
scraper = EcommerceScraper(cache_dir="./custom_cache")
```

## Best Practices

1. **Respect Rate Limits**: Built-in 2-second delays between pages
2. **Use Caching**: Enable caching for development and testing
3. **Monitor API Usage**: LLM extraction uses API calls
4. **Handle Errors**: Always wrap scraping in try-catch blocks
5. **Filter Results**: Use filtering to focus on relevant products

## Troubleshooting

### Common Issues

1. **Missing API Key**: Set OPENAI_API_KEY environment variable
2. **Site Blocking**: Some sites may block automated requests
3. **Extraction Errors**: LLM may occasionally fail to parse complex pages
4. **Rate Limiting**: Increase delays if getting blocked

### Debug Mode

```python
# Enable verbose logging
scraper = EcommerceScraper()
scraper.browser_config.verbose = True
```

## Examples

See the `main()` function in `pc_scan.py` for complete examples of:
- Basic scraping
- Multi-page scraping
- Product filtering
- Result saving
- Error handling

## License

This tool is for educational and research purposes. Always respect website terms of service and robots.txt files.
