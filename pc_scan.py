import os
import asyncio
import json
import re
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from pathlib import Path

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from pydantic import BaseModel, Field


class ProductInfo(BaseModel):
    """Product information model"""
    name: str = Field(..., description="Product name or title")
    description: Optional[str] = Field(None, description="Product description")
    price: Optional[str] = Field(None, description="Current price")
    original_price: Optional[str] = Field(None, description="Original price before discount")
    discount: Optional[str] = Field(None, description="Discount amount or percentage")
    product_url: str = Field(..., description="Direct link to the product page")
    image_url: Optional[str] = Field(None, description="Product image URL")
    availability: Optional[str] = Field(None, description="Stock status or availability")
    brand: Optional[str] = Field(None, description="Product brand")
    category: Optional[str] = Field(None, description="Product category")


class PaginationInfo(BaseModel):
    """Pagination information model"""
    current_page: int = Field(..., description="Current page number")
    total_pages: Optional[int] = Field(None, description="Total number of pages")
    next_page_url: Optional[str] = Field(None, description="URL of the next page")
    has_next: bool = Field(..., description="Whether there is a next page")


class ScrapingResult(BaseModel):
    """Complete scraping result"""
    products: List[ProductInfo] = Field(default_factory=list)
    pagination: Optional[PaginationInfo] = Field(None)
    page_url: str = Field(..., description="URL of the scraped page")


@dataclass
class SiteConfig:
    """Configuration for different ecommerce sites"""
    name: str
    base_url: str
    product_list_selectors: List[str]
    pagination_selectors: List[str]
    custom_instructions: str = ""


class EcommerceScraper:
    """Generic ecommerce scraper for computer hardware stores"""

    def __init__(self, cache_dir: str = "./cache", llm_provider: str = "openai/gpt-4o"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.llm_provider = llm_provider
        self.browser_config = BrowserConfig(
            verbose=True,
            headless=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        )

        # Site configurations for popular hardware stores
        self.site_configs = {
            "centrecom": SiteConfig(
                name="Centre Com",
                base_url="https://www.centrecom.com.au",
                product_list_selectors=[".product-item", ".product-card", ".item"],
                pagination_selectors=[".pagination", ".pager", ".page-numbers"],
                custom_instructions="Focus on computer hardware products including CPUs, GPUs, motherboards, RAM, storage devices."
            ),
            "newegg": SiteConfig(
                name="Newegg",
                base_url="https://www.newegg.com",
                product_list_selectors=[".item-container", ".item-cell"],
                pagination_selectors=[".list-tool-pagination"],
                custom_instructions="Extract computer components and electronics with detailed specifications."
            ),
            "amazon": SiteConfig(
                name="Amazon",
                base_url="https://www.amazon.com",
                product_list_selectors=["[data-component-type='s-search-result']", ".s-result-item"],
                pagination_selectors=[".a-pagination"],
                custom_instructions="Focus on computer hardware and electronics products."
            ),
            "bestbuy": SiteConfig(
                name="Best Buy",
                base_url="https://www.bestbuy.com",
                product_list_selectors=[".sku-item", ".product-item"],
                pagination_selectors=[".sr-pagination"],
                custom_instructions="Extract computer hardware, components, and electronics."
            )
        }

    def _get_site_config(self, url: str) -> SiteConfig:
        """Determine site configuration based on URL"""
        domain = urlparse(url).netloc.lower()

        for key, config in self.site_configs.items():
            if key in domain or config.base_url in url:
                return config

        # Default generic configuration
        return SiteConfig(
            name="Generic",
            base_url="",
            product_list_selectors=[".product", ".item", ".card"],
            pagination_selectors=[".pagination", ".pager"],
            custom_instructions="Extract computer hardware and electronics products."
        )

    def _create_extraction_strategy(self, site_config: SiteConfig) -> LLMExtractionStrategy:
        """Create LLM extraction strategy for the site"""

        instruction = f"""
        You are extracting product information from an ecommerce website ({site_config.name}).
        {site_config.custom_instructions}

        Extract ALL products visible on the page with the following information:
        - Product name/title
        - Description (if available)
        - Current price (extract the actual price value)
        - Original price (if there's a discount/strikethrough price)
        - Discount amount or percentage (if applicable)
        - Product URL (complete link to product page)
        - Image URL (if available)
        - Availability/stock status
        - Brand name
        - Product category

        Also extract pagination information:
        - Current page number
        - Total pages (if visible)
        - Next page URL
        - Whether there are more pages

        Be thorough and extract every product you can find. Pay attention to:
        - Sale prices vs regular prices
        - Discount percentages or amounts
        - Out of stock indicators
        - Product specifications in titles/descriptions

        Return the data in the exact JSON schema format provided.
        """

        return LLMExtractionStrategy(
            llm_config=LLMConfig(
                provider=self.llm_provider,
                api_token=os.getenv('OPENAI_API_KEY')
            ),
            schema=ScrapingResult.schema(),
            extraction_type="schema",
            instruction=instruction
        )

    async def scrape_page(self, url: str, use_cache: bool = True) -> ScrapingResult:
        """Scrape a single page for products"""

        site_config = self._get_site_config(url)
        extraction_strategy = self._create_extraction_strategy(site_config)

        run_config = CrawlerRunConfig(
            word_count_threshold=10,
            extraction_strategy=extraction_strategy,
            cache_mode=CacheMode.ENABLED if use_cache else CacheMode.BYPASS,
            page_timeout=30000,
            wait_for_images=True,
            process_iframes=False,
            remove_overlay_elements=True
        )

        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            try:
                result = await crawler.arun(url=url, config=run_config)

                if result.extracted_content:
                    # Parse the extracted JSON
                    extracted_data = json.loads(result.extracted_content)
                    scraping_result = ScrapingResult(**extracted_data)
                    scraping_result.page_url = url

                    # Resolve relative URLs
                    self._resolve_urls(scraping_result, url)

                    return scraping_result
                else:
                    return ScrapingResult(products=[], page_url=url)

            except Exception as e:
                print(f"Error scraping {url}: {str(e)}")
                return ScrapingResult(products=[], page_url=url)

    def _resolve_urls(self, result: ScrapingResult, base_url: str):
        """Resolve relative URLs to absolute URLs"""
        for product in result.products:
            if product.product_url and not product.product_url.startswith('http'):
                product.product_url = urljoin(base_url, product.product_url)
            if product.image_url and not product.image_url.startswith('http'):
                product.image_url = urljoin(base_url, product.image_url)

        if result.pagination and result.pagination.next_page_url:
            if not result.pagination.next_page_url.startswith('http'):
                result.pagination.next_page_url = urljoin(base_url, result.pagination.next_page_url)

    async def scrape_all_pages(self, start_url: str, max_pages: int = 10, use_cache: bool = True) -> List[ScrapingResult]:
        """Scrape all pages starting from the given URL"""

        all_results = []
        current_url = start_url
        page_count = 0

        print(f"Starting to scrape from: {start_url}")

        while current_url and page_count < max_pages:
            page_count += 1
            print(f"Scraping page {page_count}: {current_url}")

            result = await self.scrape_page(current_url, use_cache)
            all_results.append(result)

            print(f"Found {len(result.products)} products on page {page_count}")

            # Check for next page
            if result.pagination and result.pagination.has_next and result.pagination.next_page_url:
                current_url = result.pagination.next_page_url
                # Add delay between requests to be respectful
                await asyncio.sleep(2)
            else:
                print("No more pages found or pagination limit reached")
                break

        print(f"Completed scraping {page_count} pages")
        return all_results

    def save_results(self, results: List[ScrapingResult], filename: str = "scraping_results.json"):
        """Save scraping results to JSON file"""

        output_file = self.cache_dir / filename

        # Combine all products from all pages
        all_products = []
        for result in results:
            all_products.extend([product.dict() for product in result.products])

        # Create summary
        summary = {
            "total_products": len(all_products),
            "total_pages_scraped": len(results),
            "products": all_products,
            "scraping_metadata": {
                "pages_scraped": [result.page_url for result in results],
                "timestamp": asyncio.get_event_loop().time()
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"Results saved to: {output_file}")
        return output_file

    def filter_products(self, results: List[ScrapingResult],
                       min_price: float = None, max_price: float = None,
                       brand_filter: str = None, category_filter: str = None,
                       has_discount: bool = None) -> List[ProductInfo]:
        """Filter products based on criteria"""

        filtered_products = []

        for result in results:
            for product in result.products:
                # Price filtering
                if min_price or max_price:
                    price_str = product.price or "0"
                    price_match = re.search(r'[\d,]+\.?\d*', price_str.replace(',', ''))
                    if price_match:
                        price = float(price_match.group())
                        if min_price and price < min_price:
                            continue
                        if max_price and price > max_price:
                            continue

                # Brand filtering
                if brand_filter and product.brand:
                    if brand_filter.lower() not in product.brand.lower():
                        continue

                # Category filtering
                if category_filter and product.category:
                    if category_filter.lower() not in product.category.lower():
                        continue

                # Discount filtering
                if has_discount is not None:
                    has_product_discount = bool(product.discount or product.original_price)
                    if has_discount != has_product_discount:
                        continue

                filtered_products.append(product)

        return filtered_products


# Example usage and utility functions
async def scan_hardware_store(store_url: str, max_pages: int = 5, save_to_file: bool = True):
    """Convenience function to scan a hardware store"""

    scraper = EcommerceScraper()

    try:
        results = await scraper.scrape_all_pages(store_url, max_pages=max_pages)

        if save_to_file:
            output_file = scraper.save_results(results)
            print(f"Scraping completed. Results saved to: {output_file}")

        # Print summary
        total_products = sum(len(result.products) for result in results)
        print(f"\nScraping Summary:")
        print(f"- Total pages scraped: {len(results)}")
        print(f"- Total products found: {total_products}")

        # Show sample products
        if results and results[0].products:
            print(f"\nSample products from first page:")
            for i, product in enumerate(results[0].products[:3]):
                print(f"{i+1}. {product.name}")
                print(f"   Price: {product.price}")
                if product.discount:
                    print(f"   Discount: {product.discount}")
                print(f"   URL: {product.product_url}")
                print()

        return results

    except Exception as e:
        print(f"Error during scraping: {str(e)}")
        return []


async def main():
    """Main function with example usage"""

    # Example URLs for different hardware stores
    example_urls = [
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        "https://www.newegg.com/Desktop-Graphics-Cards/SubCategory/ID-48",
        # Add more URLs as needed
    ]

    # Choose which URL to scrape
    url_to_scrape = example_urls[0]  # Change index to test different sites

    print(f"Starting scrape of: {url_to_scrape}")

    # Run the scraper
    results = await scan_hardware_store(url_to_scrape, max_pages=3)

    if results:
        scraper = EcommerceScraper()

        # Example: Filter for products with discounts
        discounted_products = scraper.filter_products(results, has_discount=True)
        print(f"\nFound {len(discounted_products)} products with discounts")

        # Example: Filter by price range
        affordable_products = scraper.filter_products(results, max_price=500.0)
        print(f"Found {len(affordable_products)} products under $500")


if __name__ == "__main__":
    # Set your OpenAI API key
    # os.environ['OPENAI_API_KEY'] = 'your-api-key-here'

    asyncio.run(main())
