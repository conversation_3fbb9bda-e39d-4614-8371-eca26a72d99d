# PC Component Price Scraper

A powerful Python script that uses the crawl4ai library to extract PC component pricing information from multiple websites. This tool helps you gather structured data about computer components including CPUs, RAM, graphics cards, motherboards, power supplies, cases, cooling systems, and storage devices.

## Features

- **Multi-website Support**: Scrapes from popular PC component retailers (Newegg, Amazon, Best Buy, Micro Center)
- **Comprehensive Component Coverage**: Supports all major PC component categories
- **Structured Data Output**: Exports data to CSV format for easy analysis
- **Configurable**: Easily add new websites and customize scraping parameters
- **Error Handling**: Robust error handling and logging
- **Progress Tracking**: Real-time progress display with rich console output
- **Fallback Parsing**: Uses both LLM-based extraction and traditional HTML parsing
- **Rate Limiting**: Respectful scraping with configurable delays

## Supported Component Categories

- **CPU** (Processors)
- **RAM** (Memory)
- **Video Card** (Graphics Cards)
- **Motherboard**
- **Power Supply** (PSU)
- **PC Case**
- **Cooling** (Fans/Coolers)
- **Storage** (NVMe/SSD/HDD)

## Installation

1. **<PERSON>lone or download the project files**

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install crawl4ai** (if not already installed):
   ```bash
   pip install crawl4ai
   ```

## Quick Start

### Basic Usage

Run the scraper with default settings:

```bash
python pc_component_scraper.py
```

This will scrape all component categories from all configured websites and save the results to a timestamped CSV file.

### Scrape Specific Categories

```bash
python pc_component_scraper.py --categories cpu ram videocard
```

### Custom Output File

```bash
python pc_component_scraper.py --output my_components.csv
```

### Verbose Logging

```bash
python pc_component_scraper.py --verbose
```

## Configuration

The scraper uses a JSON configuration file (`scraper_config.json`) to define:

- Website URLs and selectors
- Component categories
- Extraction strategies
- Scraping settings

### Example Configuration Structure

```json
{
  "websites": [
    {
      "name": "newegg",
      "base_url": "https://www.newegg.com",
      "search_urls": {
        "cpu": "https://www.newegg.com/Processors-Desktops/Category/ID-343",
        "ram": "https://www.newegg.com/Desktop-Memory/Category/ID-147"
      },
      "selectors": {
        "product_container": ".item-container",
        "name": ".item-title",
        "price": ".price-current",
        "availability": ".item-promo",
        "link": ".item-title a"
      }
    }
  ]
}
```

### Adding New Websites

To add a new website:

1. Add a new website object to the `websites` array in `scraper_config.json`
2. Define the search URLs for each component category
3. Specify CSS selectors for extracting product information
4. Test the configuration

## Programmatic Usage

You can also use the scraper programmatically:

```python
import asyncio
from pc_component_scraper import PCComponentExtractor

async def main():
    # Initialize scraper
    scraper = PCComponentExtractor()
    
    # Scrape specific categories
    components = await scraper.scrape_all_categories(["cpu", "ram"])
    
    # Save to CSV
    output_file = scraper.save_to_csv("my_data.csv")
    
    # Get statistics
    stats = scraper.get_summary_stats()
    print(f"Extracted {stats['total_components']} components")

asyncio.run(main())
```

## Output Format

The scraper outputs data in CSV format with the following columns:

- `name`: Product name
- `price`: Price (numeric)
- `currency`: Currency (default: USD)
- `availability`: Stock status
- `specifications`: JSON string of product specifications
- `category`: Component category
- `brand`: Product brand
- `model`: Product model
- `url`: Product URL
- `website`: Source website
- `scraped_at`: Timestamp of extraction

## Examples

See `example_usage.py` for comprehensive usage examples including:

- Basic scraping
- Custom configuration
- Single website scraping
- Data filtering and analysis

Run the examples:

```bash
python example_usage.py
```

## Command Line Options

```
usage: pc_component_scraper.py [-h] [--config CONFIG] [--categories CATEGORIES [CATEGORIES ...]] [--output OUTPUT] [--verbose]

PC Component Price Scraper

optional arguments:
  -h, --help            show this help message and exit
  --config CONFIG       Configuration file path (default: scraper_config.json)
  --categories CATEGORIES [CATEGORIES ...]
                        Categories to scrape (default: all)
  --output OUTPUT       Output CSV filename (default: auto-generated with timestamp)
  --verbose             Enable verbose logging
```

## Error Handling

The scraper includes comprehensive error handling:

- Network timeouts and connection errors
- Invalid HTML structure
- Missing CSS selectors
- Rate limiting responses
- Invalid JSON configuration

All errors are logged with appropriate detail levels.

## Best Practices

1. **Respect Rate Limits**: The scraper includes built-in delays between requests
2. **Monitor Website Changes**: CSS selectors may need updates if websites change their structure
3. **Use Appropriate Categories**: Only scrape categories you need to reduce load
4. **Check Output Data**: Always verify the extracted data quality
5. **Keep Configuration Updated**: Regularly update URLs and selectors

## Troubleshooting

### Common Issues

1. **No data extracted**: Check if website selectors are still valid
2. **Connection errors**: Verify internet connection and website availability
3. **Permission errors**: Ensure write permissions for output directory
4. **Missing dependencies**: Run `pip install -r requirements.txt`

### Debug Mode

Enable verbose logging to see detailed extraction information:

```bash
python pc_component_scraper.py --verbose
```

## Future Enhancements

This is Phase 1 focusing on data extraction. Future phases will include:

- LLM-based query interface for data analysis
- Real-time price monitoring
- Price history tracking
- Advanced filtering and search capabilities
- Web dashboard for data visualization

## License

This project is provided as-is for educational and personal use. Please respect website terms of service and implement appropriate rate limiting when scraping.

## Contributing

Feel free to submit issues and enhancement requests. When adding new websites, please ensure:

1. Proper CSS selectors are defined
2. Rate limiting is respected
3. Error handling is implemented
4. Documentation is updated
