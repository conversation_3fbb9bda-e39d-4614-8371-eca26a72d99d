@echo off
echo PC Component Price Scraper
echo ========================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "pc_component_scraper.py" (
    echo Error: pc_component_scraper.py not found
    echo Please ensure you're in the correct directory
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo Error: requirements.txt not found
    echo Please ensure you're in the correct directory
    pause
    exit /b 1
)

echo Checking dependencies...
python -c "import crawl4ai, pandas, beautifulsoup4" >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Starting PC Component Scraper...
echo.

REM Run the scraper with default settings
python pc_component_scraper.py

echo.
echo Scraping completed!
echo Check the generated CSV file for results.
echo.
pause
