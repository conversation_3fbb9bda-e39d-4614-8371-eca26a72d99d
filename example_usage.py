#!/usr/bin/env python3
"""
Example usage of the PC Component Scraper

This script demonstrates how to use the PCComponentExtractor class
to scrape PC component data from multiple websites.
"""

import asyncio
import json
from pc_component_scraper import PCComponentExtractor

async def basic_usage():
    """Basic usage example"""
    print("=== Basic Usage Example ===")
    
    # Initialize the scraper
    scraper = PCComponentExtractor()
    
    # Scrape specific categories
    categories = ["cpu", "ram", "videocard"]
    components = await scraper.scrape_all_categories(categories)
    
    print(f"Extracted {len(components)} components")
    
    # Save to CSV
    output_file = scraper.save_to_csv("example_output.csv")
    print(f"Data saved to: {output_file}")
    
    # Get summary statistics
    stats = scraper.get_summary_stats()
    print("\nSummary Statistics:")
    print(json.dumps(stats, indent=2, default=str))

async def custom_config_usage():
    """Example with custom configuration"""
    print("\n=== Custom Configuration Example ===")
    
    # Create custom configuration
    custom_config = {
        "websites": [
            {
                "name": "newegg",
                "base_url": "https://www.newegg.com",
                "search_urls": {
                    "cpu": "https://www.newegg.com/Processors-Desktops/Category/ID-343"
                },
                "selectors": {
                    "product_container": ".item-container",
                    "name": ".item-title",
                    "price": ".price-current",
                    "availability": ".item-promo",
                    "link": ".item-title a"
                }
            }
        ],
        "extraction_strategy": {
            "use_llm": False
        }
    }
    
    # Save custom config
    with open("custom_config.json", "w") as f:
        json.dump(custom_config, f, indent=2)
    
    # Use custom configuration
    scraper = PCComponentExtractor("custom_config.json")
    components = await scraper.scrape_all_categories(["cpu"])
    
    print(f"Extracted {len(components)} components with custom config")

async def single_website_usage():
    """Example of scraping a single website"""
    print("\n=== Single Website Example ===")
    
    scraper = PCComponentExtractor()
    
    # Get the first website configuration
    website_config = scraper.config["websites"][0]
    
    # Scrape only CPUs from this website
    components = await scraper.scrape_website(website_config, "cpu")
    
    print(f"Extracted {len(components)} CPUs from {website_config['name']}")
    
    # Display first few components
    for i, component in enumerate(components[:3]):
        print(f"\nComponent {i+1}:")
        print(f"  Name: {component.name}")
        print(f"  Price: ${component.price}" if component.price else "  Price: Not available")
        print(f"  Availability: {component.availability}")
        print(f"  Website: {component.website}")

async def filtered_scraping():
    """Example of scraping with filtering"""
    print("\n=== Filtered Scraping Example ===")
    
    scraper = PCComponentExtractor()
    
    # Scrape all categories
    all_components = await scraper.scrape_all_categories()
    
    # Filter components with prices
    components_with_prices = [c for c in all_components if c.price is not None]
    
    # Filter by price range (e.g., $100-$500)
    mid_range_components = [
        c for c in components_with_prices 
        if 100 <= c.price <= 500
    ]
    
    print(f"Total components: {len(all_components)}")
    print(f"Components with prices: {len(components_with_prices)}")
    print(f"Mid-range components ($100-$500): {len(mid_range_components)}")
    
    # Group by category
    by_category = {}
    for component in mid_range_components:
        if component.category not in by_category:
            by_category[component.category] = []
        by_category[component.category].append(component)
    
    print("\nMid-range components by category:")
    for category, components in by_category.items():
        avg_price = sum(c.price for c in components) / len(components)
        print(f"  {category}: {len(components)} items, avg price: ${avg_price:.2f}")

async def main():
    """Run all examples"""
    try:
        await basic_usage()
        await custom_config_usage()
        await single_website_usage()
        await filtered_scraping()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure you have installed all dependencies:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    asyncio.run(main())
