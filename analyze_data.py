#!/usr/bin/env python3
"""
Data Analysis Utility for PC Component Price Data

This script provides basic analysis capabilities for the scraped PC component data.
"""

import pandas as pd
import json
import argparse
from pathlib import Path
from typing import Dict, List
import matplotlib.pyplot as plt
import seaborn as sns
from rich.console import Console
from rich.table import Table

console = Console()

class PCDataAnalyzer:
    """Analyzer for PC component pricing data"""
    
    def __init__(self, csv_file: str):
        self.csv_file = csv_file
        self.df = None
        self.load_data()
    
    def load_data(self):
        """Load data from CSV file"""
        try:
            self.df = pd.read_csv(self.csv_file)
            console.print(f"✓ Loaded {len(self.df)} records from {self.csv_file}")
            
            # Clean and prepare data
            self.df['price'] = pd.to_numeric(self.df['price'], errors='coerce')
            self.df['scraped_at'] = pd.to_datetime(self.df['scraped_at'])
            
            # Parse specifications JSON
            self.df['specifications'] = self.df['specifications'].apply(
                lambda x: json.loads(x) if isinstance(x, str) and x.strip() else {}
            )
            
        except FileNotFoundError:
            console.print(f"✗ File {self.csv_file} not found")
            raise
        except Exception as e:
            console.print(f"✗ Error loading data: {e}")
            raise
    
    def basic_stats(self):
        """Display basic statistics"""
        console.print("\n[bold blue]Basic Statistics[/bold blue]")
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Components", str(len(self.df)))
        table.add_row("Components with Prices", str(self.df['price'].notna().sum()))
        table.add_row("Unique Categories", str(self.df['category'].nunique()))
        table.add_row("Unique Websites", str(self.df['website'].nunique()))
        table.add_row("Unique Brands", str(self.df['brand'].nunique()))
        
        if self.df['price'].notna().any():
            table.add_row("Min Price", f"${self.df['price'].min():.2f}")
            table.add_row("Max Price", f"${self.df['price'].max():.2f}")
            table.add_row("Average Price", f"${self.df['price'].mean():.2f}")
            table.add_row("Median Price", f"${self.df['price'].median():.2f}")
        
        console.print(table)
    
    def category_analysis(self):
        """Analyze data by category"""
        console.print("\n[bold blue]Category Analysis[/bold blue]")
        
        category_stats = self.df.groupby('category').agg({
            'name': 'count',
            'price': ['count', 'mean', 'min', 'max', 'std']
        }).round(2)
        
        category_stats.columns = ['Total_Items', 'Items_with_Price', 'Avg_Price', 'Min_Price', 'Max_Price', 'Price_StdDev']
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Category", style="cyan")
        table.add_column("Total Items", style="green")
        table.add_column("With Price", style="green")
        table.add_column("Avg Price", style="yellow")
        table.add_column("Min Price", style="red")
        table.add_column("Max Price", style="red")
        
        for category, row in category_stats.iterrows():
            table.add_row(
                category,
                str(int(row['Total_Items'])),
                str(int(row['Items_with_Price'])),
                f"${row['Avg_Price']:.2f}" if pd.notna(row['Avg_Price']) else "N/A",
                f"${row['Min_Price']:.2f}" if pd.notna(row['Min_Price']) else "N/A",
                f"${row['Max_Price']:.2f}" if pd.notna(row['Max_Price']) else "N/A"
            )
        
        console.print(table)
    
    def website_analysis(self):
        """Analyze data by website"""
        console.print("\n[bold blue]Website Analysis[/bold blue]")
        
        website_stats = self.df.groupby('website').agg({
            'name': 'count',
            'price': ['count', 'mean']
        }).round(2)
        
        website_stats.columns = ['Total_Items', 'Items_with_Price', 'Avg_Price']
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Website", style="cyan")
        table.add_column("Total Items", style="green")
        table.add_column("With Price", style="green")
        table.add_column("Avg Price", style="yellow")
        table.add_column("Success Rate", style="blue")
        
        for website, row in website_stats.iterrows():
            success_rate = (row['Items_with_Price'] / row['Total_Items']) * 100
            table.add_row(
                website,
                str(int(row['Total_Items'])),
                str(int(row['Items_with_Price'])),
                f"${row['Avg_Price']:.2f}" if pd.notna(row['Avg_Price']) else "N/A",
                f"{success_rate:.1f}%"
            )
        
        console.print(table)
    
    def price_range_analysis(self):
        """Analyze price ranges"""
        console.print("\n[bold blue]Price Range Analysis[/bold blue]")
        
        price_data = self.df[self.df['price'].notna()]
        if len(price_data) == 0:
            console.print("No price data available for analysis")
            return
        
        # Define price ranges
        ranges = [
            (0, 50, "Under $50"),
            (50, 100, "$50-$100"),
            (100, 250, "$100-$250"),
            (250, 500, "$250-$500"),
            (500, 1000, "$500-$1000"),
            (1000, float('inf'), "Over $1000")
        ]
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Price Range", style="cyan")
        table.add_column("Count", style="green")
        table.add_column("Percentage", style="yellow")
        
        total_with_price = len(price_data)
        
        for min_price, max_price, label in ranges:
            count = len(price_data[(price_data['price'] >= min_price) & (price_data['price'] < max_price)])
            percentage = (count / total_with_price) * 100
            table.add_row(label, str(count), f"{percentage:.1f}%")
        
        console.print(table)
    
    def top_products(self, category: str = None, limit: int = 10):
        """Show top products by price"""
        console.print(f"\n[bold blue]Top {limit} Products" + (f" in {category}" if category else "") + "[/bold blue]")
        
        data = self.df[self.df['price'].notna()]
        if category:
            data = data[data['category'] == category]
        
        if len(data) == 0:
            console.print("No data available")
            return
        
        top_products = data.nlargest(limit, 'price')
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Rank", style="cyan")
        table.add_column("Product", style="green", max_width=40)
        table.add_column("Price", style="yellow")
        table.add_column("Category", style="blue")
        table.add_column("Website", style="red")
        
        for i, (_, product) in enumerate(top_products.iterrows(), 1):
            table.add_row(
                str(i),
                product['name'][:37] + "..." if len(product['name']) > 40 else product['name'],
                f"${product['price']:.2f}",
                product['category'],
                product['website']
            )
        
        console.print(table)
    
    def search_products(self, query: str, max_results: int = 20):
        """Search for products by name"""
        console.print(f"\n[bold blue]Search Results for '{query}'[/bold blue]")
        
        # Case-insensitive search in product names
        results = self.df[self.df['name'].str.contains(query, case=False, na=False)]
        
        if len(results) == 0:
            console.print("No products found")
            return
        
        # Sort by price if available
        results = results.sort_values('price', na_position='last')
        results = results.head(max_results)
        
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Product", style="green", max_width=50)
        table.add_column("Price", style="yellow")
        table.add_column("Category", style="blue")
        table.add_column("Website", style="red")
        
        for _, product in results.iterrows():
            table.add_row(
                product['name'][:47] + "..." if len(product['name']) > 50 else product['name'],
                f"${product['price']:.2f}" if pd.notna(product['price']) else "N/A",
                product['category'],
                product['website']
            )
        
        console.print(table)
        console.print(f"\nShowing {len(results)} of {len(self.df[self.df['name'].str.contains(query, case=False, na=False)])} results")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Analyze PC Component Price Data")
    parser.add_argument("csv_file", help="CSV file to analyze")
    parser.add_argument("--category", help="Filter by category for top products")
    parser.add_argument("--search", help="Search for products by name")
    parser.add_argument("--top", type=int, default=10, help="Number of top products to show")
    
    args = parser.parse_args()
    
    if not Path(args.csv_file).exists():
        console.print(f"✗ File {args.csv_file} not found")
        return
    
    try:
        analyzer = PCDataAnalyzer(args.csv_file)
        
        if args.search:
            analyzer.search_products(args.search)
        else:
            analyzer.basic_stats()
            analyzer.category_analysis()
            analyzer.website_analysis()
            analyzer.price_range_analysis()
            analyzer.top_products(args.category, args.top)
        
    except Exception as e:
        console.print(f"✗ Analysis failed: {e}")

if __name__ == "__main__":
    main()
