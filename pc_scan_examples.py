#!/usr/bin/env python3
"""
Example usage of the PC Hardware Store Scraper (pc_scan.py)

This script demonstrates various ways to use the pc_scan.py scraper
for extracting product information from ecommerce websites.
"""

import os
import asyncio
from pc_scan import EcommerceScraper, scan_hardware_store


async def basic_example():
    """Basic scraping example"""
    print("=== Basic Scraping Example ===")
    
    # Simple one-liner to scrape a store
    results = await scan_hardware_store(
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        max_pages=2,
        save_to_file=True
    )
    
    if results:
        total_products = sum(len(result.products) for result in results)
        print(f"Successfully scraped {total_products} products from {len(results)} pages")
    
    return results


async def advanced_example():
    """Advanced scraping with custom configuration"""
    print("\n=== Advanced Scraping Example ===")
    
    # Create scraper with custom settings
    scraper = EcommerceScraper(
        cache_dir="./hardware_cache",
        llm_provider="openai/gpt-4o"
    )
    
    # URLs to scrape
    urls_to_scrape = [
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        # Add more URLs here as needed
    ]
    
    all_results = []
    
    for url in urls_to_scrape:
        print(f"\nScraping: {url}")
        try:
            # Scrape multiple pages from this URL
            results = await scraper.scrape_all_pages(url, max_pages=3, use_cache=True)
            all_results.extend(results)
            
            # Show progress
            products_found = sum(len(r.products) for r in results)
            print(f"Found {products_found} products from {len(results)} pages")
            
        except Exception as e:
            print(f"Error scraping {url}: {e}")
    
    # Save combined results
    if all_results:
        output_file = scraper.save_results(all_results, "combined_hardware_results.json")
        print(f"\nAll results saved to: {output_file}")
    
    return all_results


async def filtering_example():
    """Example of filtering scraped products"""
    print("\n=== Product Filtering Example ===")
    
    # First scrape some data
    scraper = EcommerceScraper()
    results = await scraper.scrape_all_pages(
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        max_pages=2
    )
    
    if not results:
        print("No results to filter")
        return
    
    print(f"Total products scraped: {sum(len(r.products) for r in results)}")
    
    # Filter examples
    print("\n--- Filtering Examples ---")
    
    # 1. Products with discounts
    discounted = scraper.filter_products(results, has_discount=True)
    print(f"Products with discounts: {len(discounted)}")
    
    # 2. Products under $500
    affordable = scraper.filter_products(results, max_price=500.0)
    print(f"Products under $500: {len(affordable)}")
    
    # 3. Products over $1000
    premium = scraper.filter_products(results, min_price=1000.0)
    print(f"Premium products (>$1000): {len(premium)}")
    
    # 4. NVIDIA products
    nvidia_products = scraper.filter_products(results, brand_filter="nvidia")
    print(f"NVIDIA products: {len(nvidia_products)}")
    
    # 5. Combine filters: Discounted NVIDIA products under $800
    combo_filter = scraper.filter_products(
        results,
        brand_filter="nvidia",
        max_price=800.0,
        has_discount=True
    )
    print(f"Discounted NVIDIA products under $800: {len(combo_filter)}")
    
    # Show some examples
    if combo_filter:
        print("\nExample filtered products:")
        for i, product in enumerate(combo_filter[:3]):
            print(f"{i+1}. {product.name}")
            print(f"   Price: {product.price}")
            if product.original_price:
                print(f"   Original: {product.original_price}")
            if product.discount:
                print(f"   Discount: {product.discount}")
            print()


async def single_page_example():
    """Example of scraping a single page"""
    print("\n=== Single Page Scraping Example ===")
    
    scraper = EcommerceScraper()
    
    # Scrape just one page
    result = await scraper.scrape_page(
        "https://www.centrecom.com.au/nvidia-amd-graphics-cards",
        use_cache=True
    )
    
    print(f"Products found on single page: {len(result.products)}")
    
    # Show first few products
    if result.products:
        print("\nFirst 3 products:")
        for i, product in enumerate(result.products[:3]):
            print(f"{i+1}. {product.name}")
            print(f"   Price: {product.price}")
            print(f"   URL: {product.product_url}")
            print()
    
    # Check pagination info
    if result.pagination:
        print(f"Pagination info:")
        print(f"- Current page: {result.pagination.current_page}")
        print(f"- Has next page: {result.pagination.has_next}")
        if result.pagination.next_page_url:
            print(f"- Next page URL: {result.pagination.next_page_url}")


async def main():
    """Run all examples"""
    
    # Check if API key is set
    if not os.getenv('OPENAI_API_KEY'):
        print("WARNING: OPENAI_API_KEY not set. Please set it before running:")
        print("export OPENAI_API_KEY='your-api-key-here'")
        print("\nRunning examples anyway (they may fail)...\n")
    
    # Run examples
    try:
        await basic_example()
        await single_page_example()
        await filtering_example()
        await advanced_example()
        
        print("\n=== All Examples Completed ===")
        print("Check the ./cache directory for saved results")
        
    except KeyboardInterrupt:
        print("\nScraping interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")


if __name__ == "__main__":
    # Set API key here if not set in environment
    # os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
    
    asyncio.run(main())
