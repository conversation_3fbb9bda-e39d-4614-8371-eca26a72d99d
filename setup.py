#!/usr/bin/env python3
"""
Setup script for PC Component Price Scraper

This script helps set up the environment and dependencies for the scraper.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✓ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description} failed:")
        print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"✗ Python 3.8+ required, found {version.major}.{version.minor}")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_dependencies():
    """Install required Python packages"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("✗ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def setup_crawl4ai():
    """Set up crawl4ai with necessary components"""
    commands = [
        (f"{sys.executable} -m pip install crawl4ai", "Installing crawl4ai"),
        ("crawl4ai-setup", "Setting up crawl4ai browser components")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True

def create_sample_files():
    """Create sample configuration and test files"""
    print("\nCreating sample files...")
    
    # Check if config file exists
    config_file = Path("scraper_config.json")
    if config_file.exists():
        print("✓ Configuration file already exists")
    else:
        print("✗ Configuration file not found. Please ensure scraper_config.json exists.")
        return False
    
    # Create a simple test script
    test_script = Path("test_setup.py")
    test_content = '''#!/usr/bin/env python3
"""
Simple test script to verify the setup
"""

import asyncio
import sys

async def test_imports():
    """Test if all required modules can be imported"""
    try:
        import crawl4ai
        print("✓ crawl4ai imported successfully")
        
        import pandas
        print("✓ pandas imported successfully")
        
        import beautifulsoup4
        print("✓ beautifulsoup4 imported successfully")
        
        from pc_component_scraper import PCComponentExtractor
        print("✓ PCComponentExtractor imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

async def test_basic_functionality():
    """Test basic scraper functionality"""
    try:
        from pc_component_scraper import PCComponentExtractor
        scraper = PCComponentExtractor()
        print("✓ Scraper initialized successfully")
        
        # Test configuration loading
        if scraper.config:
            print("✓ Configuration loaded successfully")
            print(f"  Found {len(scraper.config.get('websites', []))} websites configured")
        else:
            print("✗ Configuration not loaded")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("Testing PC Component Scraper setup...")
    
    if not await test_imports():
        print("\\nSetup verification failed. Please check your installation.")
        sys.exit(1)
    
    if not await test_basic_functionality():
        print("\\nBasic functionality test failed.")
        sys.exit(1)
    
    print("\\n✓ All tests passed! The scraper is ready to use.")
    print("\\nTo run the scraper:")
    print("  python pc_component_scraper.py")
    print("\\nTo see examples:")
    print("  python example_usage.py")

if __name__ == "__main__":
    asyncio.run(main())
'''
    
    with open(test_script, 'w') as f:
        f.write(test_content)
    print("✓ Test script created")
    
    return True

def main():
    """Main setup function"""
    print("PC Component Price Scraper Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n✗ Failed to install dependencies")
        sys.exit(1)
    
    # Setup crawl4ai
    if not setup_crawl4ai():
        print("\n✗ Failed to setup crawl4ai")
        print("You may need to run 'crawl4ai-setup' manually")
    
    # Create sample files
    if not create_sample_files():
        print("\n✗ Failed to create sample files")
    
    print("\n" + "=" * 40)
    print("Setup completed!")
    print("\nNext steps:")
    print("1. Run the test script: python test_setup.py")
    print("2. Try the example: python example_usage.py")
    print("3. Run the scraper: python pc_component_scraper.py")
    print("\nFor help, see README.md")

if __name__ == "__main__":
    main()
